"""Use case for downloading media."""
import logging
import uuid

from src.application.dtos.download_dtos import DownloadMediaRequest, DownloadMediaResponse
from src.domain.value_objects.url import Url
from src.domain.value_objects.identifiers import ChatId, UserId
from src.domain.interfaces.services import (
    IInstagramService,
    IYouTubeService,
    ITelegramService,
    IFileService,
    IPinterestService,
    ITikTokService
)
from src.domain.entities.media_item import MediaType

logger = logging.getLogger(__name__)


class DownloadMediaUseCase:
    """Use case for downloading media from various platforms."""

    def __init__(
        self,
        instagram_service: IInstagramService,
        youtube_service: IYouTubeService,
        pinterest_service: IPinterestService,
        tiktok_service: ITikTokService,
        telegram_service: ITelegramService,
        file_service: IFileService,
    ):
        self._instagram_service = instagram_service
        self._youtube_service = youtube_service
        self._pinterest_service = pinterest_service
        self._tiktok_service = tiktok_service
        self._telegram_service = telegram_service
        self._file_service = file_service

    async def execute(self, request: DownloadMediaRequest) -> DownloadMediaResponse:
        """Execute the download media use case."""
        try:
            url = Url(request.url)
            chat_id = ChatId(request.chat_id)
            user_id = UserId(request.chat_id)

            if url.is_instagram:
                return await self._handle_instagram_download(url, chat_id, user_id)
            elif url.is_youtube:
                media_type = request.media_type
                if media_type == "auto" and request.video_format == "mp3":
                    media_type = "audio"

                # Handle YouTube based on media type
                if media_type == "audio":
                    return await self._handle_youtube_audio_download(url, chat_id, user_id, request)
                elif media_type == "video":
                    return await self._handle_youtube_download(url, chat_id, user_id, request)
                else:  # auto - default to video
                    return await self._handle_youtube_download(url, chat_id, user_id, request)
            elif url.is_pinterest:
                return await self._handle_pinterest_download(url, chat_id, user_id)
            elif url.is_tiktok:
                return await self._handle_tiktok_download(url, chat_id, user_id)
            else:
                await self._telegram_service.send_message(
                    chat_id,
                    "Invalid URL. Please send a valid Instagram, YouTube, Pinterest yoki TikTok URL."
                )
                return DownloadMediaResponse(
                    success=False,
                    message="Invalid URL format"
                )

        except ValueError as e:
            logger.error(f"Validation error: {e}")
            return DownloadMediaResponse(
                success=False,
                message=f"Validation error: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected error in download use case: {e}")
            return DownloadMediaResponse(
                success=False,
                message="An unexpected error occurred"
            )

    async def _handle_instagram_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle Instagram media download."""
        try:
            # Get media info
            media_info = await self._instagram_service.get_media_info(url)

            if media_info.caption:
                await self._telegram_service.send_message(
                    chat_id,
                    f"Caption: {media_info.caption}"
                )

            if media_info.media_type == MediaType.ALBUM:
                return await self._handle_album_download(url, chat_id, user_id)
            else:
                return await self._handle_single_media_download(
                    media_info, url, chat_id, user_id
                )

        except Exception as e:
            logger.error(f"Error handling Instagram download: {e}")
            await self._telegram_service.send_message(
                chat_id,
                "Instagram'dan media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            )
            return DownloadMediaResponse(
                success=False,
                message="Instagram'dan media yuklab olishda xatolik yuz berdi."
            )

    async def _handle_youtube_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId,
        request: DownloadMediaRequest
    ) -> DownloadMediaResponse:
        """Handle YouTube video download."""
        try:
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            result = await self._youtube_service.download_video(url, bot_username, request.video_format)
            
            if not result.success:
                await self._telegram_service.send_message(
                    chat_id,
                    "YouTube'dan video yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                )
                return DownloadMediaResponse(
                    success=False,
                    message="YouTube'dan video yuklab olishda xatolik yuz berdi."
                )

            file_id = None
            try:
                if result.telegram_file_id:
                    file_id = await self._telegram_service.send_video_by_file_id(
                        chat_id=chat_id,
                        file_id=result.telegram_file_id,
                        caption=result.title
                    )
                else:
                    file_id = await self._telegram_service.send_video(
                        chat_id=chat_id,
                        video_path=result.file_path,
                        caption=result.title,
                        duration=result.duration,
                        width=result.width,
                        height=result.height,
                        thumbnail_path=result.thumbnail_path
                    )
            except Exception as telegram_error:
                error_str = str(telegram_error).lower()
                if "topic_closed" in error_str or "mavzu yopilgan" in error_str:
                    logger.warning(f"Topic closed error, retrying without thread context: {telegram_error}")
                    try:
                        # Retry without any thread context
                        if result.telegram_file_id:
                            file_id = await self._telegram_service.send_video_by_file_id(
                                chat_id=chat_id,
                                file_id=result.telegram_file_id,
                                caption=result.title
                            )
                        else:
                            file_id = await self._telegram_service.send_video(
                                chat_id=chat_id,
                                video_path=result.file_path,
                                caption=result.title,
                                duration=result.duration,
                                width=result.width,
                                height=result.height,
                                thumbnail_path=result.thumbnail_path
                            )
                    except Exception as retry_error:
                        logger.error(f"Failed to send video even after retry: {retry_error}")
                        await self._telegram_service.send_message(
                            chat_id,
                            "Video yuborishda xatolik yuz berdi. Iltimos, boshqa mavzuda yoki shaxsiy xabarda urinib ko'ring."
                        )
                        raise retry_error
                else:
                    # Re-raise other errors
                    raise telegram_error



            # Cleanup files (only for local downloads)
            if result.file_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.file_path)
            if result.thumbnail_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.thumbnail_path)

            return DownloadMediaResponse(
                success=True,
                message="Video yuklandi va yuborildi",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling YouTube download: {e}")

            # Send user-friendly message
            user_message = "YouTube video yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            try:
                await self._telegram_service.send_message(chat_id, user_message)
            except Exception as msg_error:
                logger.error(f"Failed to send error message: {msg_error}")

            return DownloadMediaResponse(
                success=False,
                message=user_message
            )

    async def _handle_youtube_audio_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId,
        request: DownloadMediaRequest
    ) -> DownloadMediaResponse:
        """Handle YouTube audio download."""
        try:
            # Get bot username for FastSaver API
            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            result = await self._youtube_service.download_audio(url, bot_username, request.video_format)
            
            if not result.success:
                logger.error(f"YouTube audio download failed: {result.message}")

                user_message = "YouTube audio yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                await self._telegram_service.send_message(chat_id, user_message)

                try:
                    from src.infrastructure.services.error_notification_service import get_error_notification_service
                    error_service = get_error_notification_service(request.bot_token)
                    await error_service.notify_admin_error(f"YouTube audio download failed: {result.message}")
                except Exception as notification_error:
                    logger.error(f"Failed to send admin notification: {notification_error}")

                return DownloadMediaResponse(
                    success=False,
                    message=user_message
                )

            file_id = None
            try:
                if result.telegram_file_id:
                    file_id = await self._telegram_service.send_audio_by_file_id(
                        chat_id=chat_id,
                        file_id=result.telegram_file_id,
                        caption=result.title,
                        title=result.title,
                        duration=result.duration
                    )
                else:
                    # Use local file from yt-dlp
                    file_id = await self._telegram_service.send_audio(
                        chat_id=chat_id,
                        audio_path=result.file_path,
                        caption=result.title,
                        title=result.title,
                        duration=result.duration
                    )
            except Exception as telegram_error:
                error_str = str(telegram_error).lower()
                if "topic_closed" in error_str or "mavzu yopilgan" in error_str:
                    logger.warning(f"Topic closed error, retrying without thread context: {telegram_error}")
                    try:
                        # Retry without any thread context
                        if result.telegram_file_id:
                            file_id = await self._telegram_service.send_audio_by_file_id(
                                chat_id=chat_id,
                                file_id=result.telegram_file_id,
                                caption=result.title,
                                title=result.title,
                                duration=result.duration
                            )
                        else:
                            file_id = await self._telegram_service.send_audio(
                                chat_id=chat_id,
                                audio_path=result.file_path,
                                caption=result.title,
                                title=result.title,
                                duration=result.duration
                            )
                    except Exception as retry_error:
                        logger.error(f"Failed to send audio even after retry: {retry_error}")
                        await self._telegram_service.send_message(
                            chat_id,
                            "Audio yuborishda xatolik yuz berdi. Iltimos, boshqa mavzuda yoki shaxsiy xabarda urinib ko'ring."
                        )
                        raise retry_error
                else:
                    # Re-raise other errors
                    raise telegram_error

            # Cleanup files (only for local downloads)
            if result.file_path and not result.telegram_file_id:
                await self._file_service.cleanup_file(result.file_path)

            return DownloadMediaResponse(
                success=True,
                message="Audio yuklandi va yuborildi",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling YouTube audio download: {e}")

            user_message = "YouTube audio yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            try:
                await self._telegram_service.send_message(chat_id, user_message)
            except Exception as msg_error:
                logger.error(f"Failed to send error message: {msg_error}")

            return DownloadMediaResponse(
                success=False,
                message=user_message
            )

    async def _handle_single_media_download(
        self,
        media_info,
        url: Url,
        chat_id: ChatId,
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle single media item download."""
        try:
            media_content = await self._instagram_service.download_media(media_info)

            extension = ".mp4" if media_info.media_type == MediaType.VIDEO else ".jpg"
            unique_id = str(uuid.uuid4())
            filename = f"{unique_id}{extension}"
            file_path = await self._file_service.save_file(media_content, filename)

            if media_info.media_type == MediaType.VIDEO:
                file_id = await self._telegram_service.send_video(
                    chat_id=chat_id,
                    video_path=file_path,
                    caption=media_info.caption,
                    duration=media_info.duration
                )
            else:
                file_id = await self._telegram_service.send_photo(
                    chat_id=chat_id,
                    photo_path=file_path,
                    caption=media_info.caption
                )

            await self._file_service.cleanup_file(file_path)

            return DownloadMediaResponse(
                success=True,
                message="Media sent successfully",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error handling single media download: {e}")
            raise

    async def _handle_album_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle album download with caching."""
        try:
            # Download fresh album
            album = await self._instagram_service.get_album_info(url)

            await self._telegram_service.send_message(
                chat_id,
                f"📸 Album detected with {album.total_items} items. Downloading..."
            )

            file_ids = []
            for item in album.items:
                try:
                    media_content = await self._instagram_service.download_media(item)

                    extension = ".mp4" if item.media_type == MediaType.VIDEO else ".jpg"
                    unique_id = str(uuid.uuid4())
                    filename = f"{unique_id}{extension}"
                    file_path = await self._file_service.save_file(media_content, filename)

                    caption = f"Album item {item.index}/{item.total_items}"
                    if item.media_type == MediaType.VIDEO:
                        file_id = await self._telegram_service.send_video(
                            chat_id=chat_id,
                            video_path=file_path,
                            caption=caption
                        )

                    else:
                        file_id = await self._telegram_service.send_photo(
                            chat_id=chat_id,
                            photo_path=file_path,
                            caption=caption
                        )

                    file_ids.append(file_id)

                    await self._file_service.cleanup_file(file_path)

                except Exception as item_error:
                    logger.error(f"Error processing album item {item.index}: {item_error}")
                    await self._telegram_service.send_message(
                        chat_id,
                        f"Error downloading album item {item.index}. Skipping..."
                    )
                    continue

            return DownloadMediaResponse(
                success=True,
                message=f"Album with {len(file_ids)} items sent successfully",
                file_id=file_ids[0] if file_ids else None
            )

        except Exception as e:
            logger.error(f"Error handling album download: {e}")
            raise

    async def _handle_pinterest_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle Pinterest media download."""
        try:
            media_info = await self._pinterest_service.get_media_info(url)
            if media_info.caption:
                await self._telegram_service.send_message(
                    chat_id,
                    f"Caption: {media_info.caption}"
                )

            return await self._handle_single_media_download(
                media_info, url, chat_id, user_id
            )
        except Exception as e:
            logger.error(f"Error handling Pinterest download: {e}")
            await self._telegram_service.send_message(
                chat_id,
                "Pinterest'dan media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            )
            return DownloadMediaResponse(
                success=False,
                message="Pinterest'dan media yuklab olishda xatolik yuz berdi."
            )

    async def _handle_tiktok_download(
        self,
        url: Url,
        chat_id: ChatId,
        user_id: UserId
    ) -> DownloadMediaResponse:
        """Handle TikTok media download."""
        try:
            media_info = await self._tiktok_service.get_media_info(url)

            if media_info.caption:
                await self._telegram_service.send_message(
                    chat_id,
                    f"Caption: {media_info.caption}"
                )

            video_result = await self._handle_single_media_download(
                media_info, url, chat_id, user_id
            )

            if media_info.music_url:
                try:
                    music_data = await self._tiktok_service.download_music(media_info.music_url)
                    music_filename = f"tiktok_music_{url.extract_tiktok_video_id()}.mp3"
                    music_path = await self._file_service.save_file(music_data, music_filename)

                    await self._telegram_service.send_audio(
                        chat_id,
                        music_path,
                        caption="🎵 TikTok musiqa"
                    )

                    await self._file_service.delete_file(music_path)

                except Exception as e:
                    logger.warning(f"Failed to download TikTok music: {e}")

            if media_info.thumbnail_url:
                try:
                    thumb_data = await self._tiktok_service.download_thumbnail(media_info.thumbnail_url)
                    thumb_filename = f"tiktok_thumb_{url.extract_tiktok_video_id()}.jpg"
                    thumb_path = await self._file_service.save_file(thumb_data, thumb_filename)

                    await self._telegram_service.send_photo(
                        chat_id,
                        thumb_path,
                        caption="🖼️ TikTok rasm"
                    )

                    await self._file_service.delete_file(thumb_path)

                except Exception as e:
                    logger.warning(f"Failed to download TikTok thumbnail: {e}")

            return video_result

        except Exception as e:
            logger.error(f"Error handling TikTok download: {e}")
            await self._telegram_service.send_message(
                chat_id,
                "TikTok'dan media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            )
            return DownloadMediaResponse(
                success=False,
                message="TikTok'dan media yuklab olishda xatolik yuz berdi."
            )
