"""Error notification service implementation."""
import logging
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from src.domain.interfaces.error_notification_service import IErrorNotificationService
from src.infrastructure.config.settings import settings


logger = logging.getLogger(__name__)


class ErrorNotificationService(IErrorNotificationService):
    """Simple error notification service implementation."""

    def __init__(self, bot_token: str):
        self.bot_token = bot_token
        self.bot = Bot(token=bot_token)
        self.admin_chat_id = settings.admin.chat_id

    async def notify_admin_error(self, error_message: str) -> None:
        """Notify admin about an error."""
        try:
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=f"🚨 Bot Error:\n{error_message}"
            )
        except TelegramError as e:
            logger.error(f"Failed to send error notification to admin: {e}")
        except Exception as e:
            logger.error(f"Unexpected error while sending admin notification: {e}")


def get_error_notification_service(bot_token: str) -> ErrorNotificationService:
    """Get error notification service instance."""
    return ErrorNotificationService(bot_token)
