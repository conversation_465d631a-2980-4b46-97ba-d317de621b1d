"""Error notification service implementation."""
import logging
from telegram import <PERSON><PERSON>
from telegram.error import TelegramError

from src.domain.interfaces.error_notification_service import IErrorNotificationService
from src.infrastructure.config.settings import settings


logger = logging.getLogger(__name__)


class ErrorNotificationService(IErrorNotificationService):
    """Simple error notification service implementation."""

    def __init__(self, bot_token: str = None):
        # Use admin bot token for notifications, fallback to provided token
        self.bot_token = settings.admin.bot_token or bot_token
        self.bot = Bot(token=self.bot_token) if self.bot_token else None
        self.admin_chat_id = settings.admin.chat_id

    async def notify_admin_error(self, error_message: str) -> None:
        """Notify admin about an error."""
        if not self.bot or not self.admin_chat_id:
            logger.warning("Admin notification not configured - missing bot token or chat ID")
            return

        try:
            await self.bot.send_message(
                chat_id=self.admin_chat_id,
                text=f"🚨 Bot Error:\n{error_message}"
            )
            logger.info(f"Admin notification sent successfully")
        except TelegramError as e:
            logger.error(f"Failed to send error notification to admin: {e}")
        except Exception as e:
            logger.error(f"Unexpected error while sending admin notification: {e}")


def get_error_notification_service(bot_token: str) -> ErrorNotificationService:
    """Get error notification service instance."""
    return ErrorNotificationService(bot_token)
