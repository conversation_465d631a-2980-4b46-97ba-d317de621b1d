"""URL value object."""
import re
from dataclasses import dataclass
from urllib.parse import urlparse
from typing import Optional


@dataclass(frozen=True)
class Url:
    """Value object for URLs."""
    value: str

    def __post_init__(self):
        if not self.value:
            raise ValueError("URL cannot be empty")
        
        # Basic URL validation
        parsed = urlparse(self.value)
        if not parsed.scheme or not parsed.netloc:
            raise ValueError(f"Invalid URL format: {self.value}")

    @property
    def domain(self) -> str:
        """Get the domain from the URL."""
        return urlparse(self.value).netloc.lower()

    @property
    def is_instagram(self) -> bool:
        """Check if URL is from Instagram (including stories)."""
        pattern = r'https?://(www\.)?instagram\.com/([A-Za-z0-9_.]+/)?(p|reel|tv|s|share|stories)/[A-Za-z0-9_-]+/?(\?.*)?$'
        return bool(re.match(pattern, self.value))

    @property
    def is_youtube(self) -> bool:
        """Check if URL is from YouTube."""
        pattern = r'(?:https?://)?(?:www\.)?(?:youtube\.com/(?:watch\?v=|embed/|v/|shorts/|live/)|youtu\.be/)([^"&?/ ]{11})'
        return bool(re.match(pattern, self.value))

    @property
    def is_pinterest(self) -> bool:
        """Check if URL is from Pinterest."""
        pattern = r'https?://(www\.)?(pin\.it|pinterest\.[a-z]+)/[A-Za-z0-9/_\-]+'
        return bool(re.match(pattern, self.value))

    @property
    def is_tiktok(self) -> bool:
        """Check if URL is from TikTok."""
        pattern = r'https?://(www\.)?(tiktok\.com|vm\.tiktok\.com)/.*'
        return bool(re.match(pattern, self.value))

    def extract_instagram_shortcode(self) -> Optional[str]:
        """Extract shortcode from Instagram URL."""
        if not self.is_instagram:
            return None
            
        parsed = urlparse(self.value)
        path_parts = [part for part in parsed.path.split('/') if part]
        
        # Handle URLs with usernames: instagram.com/username/reel/shortcode
        # or without usernames: instagram.com/reel/shortcode
        if len(path_parts) >= 2:
            # Check if the second-to-last part is a media type (p, reel, tv, s, share)
            media_types = ['p', 'reel', 'tv', 's', 'share']
            if path_parts[-2] in media_types:
                return path_parts[-1]
            # If not, assume the last part is the shortcode
            return path_parts[-1]
        elif len(path_parts) == 1:
            return path_parts[0]
        else:
            return None

    def extract_youtube_video_id(self) -> Optional[str]:
        """Extract video ID from YouTube URL."""
        if not self.is_youtube:
            return None
        
        patterns = [
            r'(?:youtube\.com/(?:[^/]+/.+/|(?:v|e(?:mbed)?|shorts|live)/|.*[?&]v=)|youtu\.be/)([^"&?/ ]{11})',
            r'(?:www\.)?youtube\.com/watch\?v=([^&]+)',
            r'(?:www\.)?youtu\.be/([^?]+)',
            r'(?:www\.)?youtube\.com/embed/([^?]+)',
            r'(?:www\.)?youtube\.com/v/([^?]+)',
            r'(?:www\.)?youtube\.com/shorts/([^?]+)',
            r'(?:www\.)?youtube\.com/live/([^?&/]+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, self.value)
            if match:
                return match.group(1)
        return None

    def extract_pinterest_shortcode(self) -> Optional[str]:
        """Extract shortcode (pin ID) from Pinterest URL."""
        if not self.is_pinterest:
            return None
        parsed = urlparse(self.value)
        path_parts = [part for part in parsed.path.split('/') if part]
        # Pinterest shortlink: pin.it/SHORTCODE
        # Pinterest full: pinterest.com/pin/SHORTCODE
        if 'pin.it' in self.domain and len(path_parts) == 1:
            return path_parts[0]
        if 'pinterest.' in self.domain and 'pin' in path_parts:
            idx = path_parts.index('pin')
            if idx + 1 < len(path_parts):
                return path_parts[idx + 1]
        # fallback: last part
        if path_parts:
            return path_parts[-1]
        return None

    def extract_tiktok_video_id(self) -> Optional[str]:
        """Extract video ID from TikTok URL."""
        if not self.is_tiktok:
            return None

        # TikTok URL patterns:
        # https://www.tiktok.com/@username/video/1234567890123456789
        # https://vm.tiktok.com/SHORTCODE/
        # https://tiktok.com/@username/video/1234567890123456789

        parsed = urlparse(self.value)
        path_parts = [part for part in parsed.path.split('/') if part]

        # Handle vm.tiktok.com short URLs
        if 'vm.tiktok.com' in self.domain and len(path_parts) >= 1:
            return path_parts[0]

        # Handle regular tiktok.com URLs
        if 'tiktok.com' in self.domain:
            # Look for video ID in path
            if 'video' in path_parts:
                video_idx = path_parts.index('video')
                if video_idx + 1 < len(path_parts):
                    video_id = path_parts[video_idx + 1]
                    # Remove query parameters if present
                    return video_id.split('?')[0]

        return None
